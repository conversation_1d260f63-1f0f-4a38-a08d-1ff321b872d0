'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { db, ChatRoom as ChatRoomType, ChatMessage, supabase } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import ChatMessageComponent from './ChatMessage';
import ChatInput from './ChatInput';

interface ChatRoomProps {
  room: ChatRoomType;
  onClose?: () => void;
}

export default function ChatRoom({ room, onClose }: ChatRoomProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Load messages
  useEffect(() => {
    const loadMessages = async () => {
      try {
        setLoading(true);
        const roomMessages = await db.getChatMessages(room.id);
        setMessages(roomMessages);
      } catch (error) {
        console.error('Error loading messages:', error);
        setError('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [room.id]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user?.id) return;

    console.log('Setting up realtime subscriptions for room:', room.id);

    // Use a single channel for both messages and typing
    const channelName = `chat_room_${room.id}_${user.id}`;

    const channel = supabase
      .channel(channelName, {
        config: {
          presence: {
            key: user.id,
          },
          private: false,
        },
      })
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${room.id}`,
        },
        async (payload) => {
          console.log('New message received:', payload);

          try {
            // Add the new message directly if we have enough info
            if (payload.new && payload.new.sender_id !== user.id) {
              const newMessage = payload.new as ChatMessage;
              setMessages(prev => {
                // Avoid duplicates
                if (prev.some(msg => msg.id === newMessage.id)) {
                  return prev;
                }
                return [...prev, newMessage];
              });
            }
          } catch (error) {
            console.error('Error processing new message:', error);
            // Fallback: reload messages
            try {
              const updatedMessages = await db.getChatMessages(room.id, 10);
              setMessages(prev => {
                const lastIds = prev.slice(-5).map(m => m.id);
                const newMessages = updatedMessages.filter(m => !lastIds.includes(m.id));
                return [...prev, ...newMessages];
              });
            } catch (reloadError) {
              console.error('Error reloading messages:', reloadError);
            }
          }
        }
      )
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const typingUserIds = Object.keys(state).filter(userId => {
          if (userId === user.id) return false;
          const presenceData = state[userId]?.[0] as { typing?: boolean };
          return presenceData?.typing === true;
        });
        setTypingUsers(typingUserIds);
      })
      .subscribe((status, err) => {
        console.log('Chat room subscription status:', status, err);
        if (status === 'SUBSCRIBED') {
          setError(''); // Clear any previous errors
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Chat room subscription error:', err);
          setError('Failed to connect to chat. Please refresh the page.');
        } else if (status === 'TIMED_OUT') {
          setError('Chat connection timed out. Please refresh the page.');
        } else if (status === 'CLOSED') {
          console.log('Chat room subscription closed');
        }
      });

    return () => {
      console.log('Cleaning up realtime subscriptions for room:', room.id);

      if (channel) {
        // Clear typing status before leaving
        channel.untrack().catch(() => {
          // Ignore errors when untracking
        });
        
        channel.unsubscribe().then(() => {
          console.log('Successfully unsubscribed from chat room channel');
        }).catch((error) => {
          console.error('Error unsubscribing from chat room channel:', error);
        });
      }
    };
  }, [room.id, user?.id]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Update last read timestamp
  useEffect(() => {
    if (user?.id && messages.length > 0) {
      db.updateLastReadAt(room.id, user.id);
    }
  }, [room.id, user?.id, messages]);

  const handleSendMessage = async (content: string) => {
    if (!user?.id || !content.trim()) return;

    try {
      const messageData = {
        room_id: room.id,
        sender_id: user.id,
        message: content.trim(),
        message_type: 'text' as const
      };

      await db.sendChatMessage(messageData);
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
    }
  };

  const handleTyping = () => {
    if (!user?.id) return;

    // Update typing status
    setIsTyping(true);
    
    // Send typing indicator via presence
    const channel = supabase.channel(`chat_room_${room.id}_${user.id}`);
    if (channel) {
      channel.track({ typing: true }).catch((error) => {
        console.error('Error tracking typing status:', error);
      });
    }

    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      if (channel) {
        channel.track({ typing: false }).catch((error) => {
          console.error('Error clearing typing status:', error);
        });
      }
    }, 3000);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {room.name || 'Chat Room'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {room.type === 'applicant_admin' ? 'Application Discussion' : 'Admin Chat'}
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        )}

        <AnimatePresence>
          {messages.map((message) => (
            <ChatMessageComponent
              key={message.id}
              message={message}
              isOwn={message.sender_id === user?.id}
            />
          ))}
        </AnimatePresence>

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"
          >
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span>Someone is typing...</span>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          onSendMessage={handleSendMessage}
          onTyping={handleTyping}
          disabled={loading}
        />
      </div>
    </div>
  );
}
